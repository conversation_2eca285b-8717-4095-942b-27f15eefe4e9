<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "About",
});

// 页面状态
const isLoaded = ref(false);

// 公司数据
const companyStats = ref([
  {
    icon: "mdi:calendar",
    number: "2024",
    label: "成立年份",
    suffix: "年",
  },
  {
    icon: "mdi:account-group",
    number: "50",
    label: "团队成员",
    suffix: "+",
  },
  {
    icon: "mdi:trophy",
    number: "10",
    label: "软件著作权",
    suffix: "+",
  },
  {
    icon: "mdi:handshake",
    number: "100",
    label: "合作客户",
    suffix: "+",
  },
]);

// 核心团队数据
const coreTeam = ref([
  {
    name: "技术研发团队",
    description:
      "来自河海大学等知名院校，专业背景涵盖水利工程、计算机科学等领域",
    icon: "mdi:code-tags",
    skills: ["AI算法", "大数据", "云计算", "物联网"],
  },
  {
    name: "项目管理团队",
    description: "多人具有高级项目管理师资格，丰富的水利信息化项目管理经验",
    icon: "mdi:account-tie",
    skills: ["项目管理", "需求分析", "质量控制", "风险管控"],
  },
  {
    name: "行业专家团队",
    description: "深耕水利行业多年，对业务场景和技术需求有深刻理解",
    icon: "mdi:school",
    skills: ["水利工程", "防汛减灾", "水资源管理", "生态保护"],
  },
]);

// 发展历程数据
const milestones = ref([
  {
    year: "2024",
    title: "公司成立",
    description: "怀川科技（北京）有限公司正式成立，获得中关村高新技术企业证书",
    icon: "mdi:rocket-launch",
    achievements: ["中关村高新技术企业认证", "10+软件著作权", "核心团队组建"],
  },
  {
    year: "2024",
    title: "技术突破",
    description: "在AI大模型、大数据分析等前沿技术领域取得重要进展",
    icon: "mdi:brain",
    achievements: ["AI算法优化", "大数据平台搭建", "云原生架构"],
  },
  {
    year: "2024",
    title: "市场拓展",
    description: "与多家水利管理部门建立合作关系，项目成功落地",
    icon: "mdi:trending-up",
    achievements: ["客户合作拓展", "项目成功交付", "行业影响力提升"],
  },
]);

// 企业文化数据
const cultureValues = ref([
  {
    icon: "mdi:lightbulb-on",
    title: "创新驱动",
    description: "持续技术创新，引领行业发展",
    details: ["前沿技术研究", "产品持续迭代", "创新思维培养"],
  },
  {
    icon: "mdi:account-heart",
    title: "客户至上",
    description: "以客户需求为导向，提供优质服务",
    details: ["深度需求理解", "定制化解决方案", "7×24小时支持"],
  },
  {
    icon: "mdi:handshake",
    title: "合作共赢",
    description: "与合作伙伴共同成长，实现价值共创",
    details: ["开放合作态度", "生态伙伴建设", "互利共赢理念"],
  },
  {
    icon: "mdi:leaf",
    title: "绿色发展",
    description: "致力于生态保护，促进可持续发展",
    details: ["环保理念践行", "绿色技术应用", "生态价值创造"],
  },
]);

// 事件处理
const handleContact = () => {
  console.log("联系我们");
};

const handleJoinUs = () => {
  console.log("加入我们");
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);

  // 数字动画效果
  const animateNumbers = () => {
    const numbers = document.querySelectorAll(".stat-number[data-count]");
    numbers.forEach((number) => {
      const target = parseInt(number.getAttribute("data-count"));
      let current = 0;
      const increment = target / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        number.textContent = Math.floor(current);
      }, 50);
    });
  };

  setTimeout(animateNumbers, 500);
});
</script>

<template>
  <div class="about" :class="{ loaded: isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 12" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:domain" class="badge-icon" />
            <span>关于怀川科技</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">怀数智之力</span>
            <span class="title-line title-highlight">护青绿山川</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            专注智慧水利行业的现代化科技型企业<br />
            <strong>以科技创新驱动水利行业数字化转型</strong>
          </p>

          <div class="hero-stats fade-in-up delay-3">
            <div
              class="stat-item"
              v-for="stat in companyStats"
              :key="stat.label"
            >
              <div class="stat-icon">
                <Icon :icon="stat.icon" />
              </div>
              <div class="stat-number" :data-count="stat.number">0</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="company-hub">
              <div class="hub-core">
                <Icon icon="mdi:water-circle" class="core-icon" />
                <div class="core-pulse"></div>
              </div>
              <div class="hub-ring ring-outer"></div>
              <div class="hub-ring ring-middle"></div>
              <div class="hub-ring ring-inner"></div>
            </div>

            <div class="value-constellation">
              <div class="constellation-node node-1">
                <div class="node-icon">
                  <Icon icon="mdi:lightbulb-on" />
                </div>
                <div class="node-label">创新</div>
              </div>
              <div class="constellation-node node-2">
                <div class="node-icon">
                  <Icon icon="mdi:account-heart" />
                </div>
                <div class="node-label">客户至上</div>
              </div>
              <div class="constellation-node node-3">
                <div class="node-icon">
                  <Icon icon="mdi:handshake" />
                </div>
                <div class="node-label">合作</div>
              </div>
              <div class="constellation-node node-4">
                <div class="node-icon">
                  <Icon icon="mdi:leaf" />
                </div>
                <div class="node-label">绿色</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 公司介绍 -->
    <section class="company-intro">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:office-building" />
            <span>公司简介</span>
          </div>
          <h2 class="section-title">怀川科技简介</h2>
          <p class="section-subtitle">专业的智慧水利解决方案提供商</p>
        </div>

        <div class="intro-content">
          <div class="intro-text">
            <div class="intro-paragraph">
              <div class="paragraph-icon">
                <Icon icon="mdi:target" />
              </div>
              <div class="paragraph-content">
                <h3>专业定位</h3>
                <p>
                  怀川科技（北京）有限公司，是一家专注于智慧水利行业的现代化科技型企业，主要面向水利行业管理部门用户，提供防洪减灾、水资源管理、水利工程运行管理、河湖管理、水环境保护等业务领域数字化建设相关的规划咨询、产品销售、软件研发和系统集成相关专业服务。
                </p>
              </div>
            </div>

            <div class="intro-paragraph">
              <div class="paragraph-icon">
                <Icon icon="mdi:account-group" />
              </div>
              <div class="paragraph-content">
                <h3>团队实力</h3>
                <p>
                  公司总部设置在北京，研发中心在江苏，成员主要毕业于河海大学等水利或计算机相关院校和专业，多人具有高级项目管理师资格，有丰富的水利信息化项目管理和实施经验。
                </p>
              </div>
            </div>

            <div class="intro-paragraph">
              <div class="paragraph-icon">
                <Icon icon="mdi:rocket-launch" />
              </div>
              <div class="paragraph-content">
                <h3>技术创新</h3>
                <p>
                  公司注重锐意革新，致力于利用先进信息技术包括AI大模型、大数据、云计算、卫星遥感、无人机&船等，提升水利行业数字化建设专业服务水平，促进人与自然和谐发展，已于2024年初获得中关村高新技术企业证书，并取得10余项相关软著。
                </p>
              </div>
            </div>
          </div>

          <div class="intro-visual">
            <div class="visual-container">
              <div class="tech-constellation">
                <div class="tech-node node-ai">
                  <Icon icon="mdi:brain" />
                  <span>AI大模型</span>
                </div>
                <div class="tech-node node-data">
                  <Icon icon="mdi:database" />
                  <span>大数据</span>
                </div>
                <div class="tech-node node-cloud">
                  <Icon icon="mdi:cloud" />
                  <span>云计算</span>
                </div>
                <div class="tech-node node-iot">
                  <Icon icon="mdi:wifi" />
                  <span>物联网</span>
                </div>
                <div class="tech-node node-satellite">
                  <Icon icon="mdi:satellite-variant" />
                  <span>遥感</span>
                </div>
                <div class="tech-node node-drone">
                  <Icon icon="mdi:quadcopter" />
                  <span>无人机</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心团队 -->
    <section class="core-team">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:account-group" />
            <span>核心团队</span>
          </div>
          <h2 class="section-title">专业团队实力</h2>
          <p class="section-subtitle">汇聚行业精英，打造专业服务团队</p>
        </div>

        <div class="team-grid">
          <div v-for="(team, index) in coreTeam" :key="index" class="team-card">
            <div class="team-visual">
              <div class="team-icon">
                <Icon :icon="team.icon" />
              </div>
              <div class="team-bg"></div>
            </div>

            <div class="team-content">
              <h3 class="team-name">{{ team.name }}</h3>
              <p class="team-description">{{ team.description }}</p>

              <div class="team-skills">
                <div class="skills-title">专业技能</div>
                <div class="skills-list">
                  <span
                    class="skill-tag"
                    v-for="skill in team.skills"
                    :key="skill"
                    >{{ skill }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 发展历程 -->
    <section class="development-timeline">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:timeline" />
            <span>发展历程</span>
          </div>
          <h2 class="section-title">成长足迹</h2>
          <p class="section-subtitle">记录每一个重要的发展节点</p>
        </div>

        <div class="timeline-container">
          <div class="timeline-track"></div>
          <div
            v-for="(milestone, index) in milestones"
            :key="index"
            class="milestone-item"
            :class="`milestone-${index + 1}`"
          >
            <div class="milestone-marker">
              <div class="marker-inner">
                <Icon :icon="milestone.icon" />
              </div>
              <div class="marker-year">{{ milestone.year }}</div>
            </div>

            <div class="milestone-content">
              <h3 class="milestone-title">{{ milestone.title }}</h3>
              <p class="milestone-description">{{ milestone.description }}</p>

              <div class="milestone-achievements">
                <div
                  class="achievement"
                  v-for="achievement in milestone.achievements"
                  :key="achievement"
                >
                  <Icon icon="mdi:check-circle" class="achievement-icon" />
                  <span>{{ achievement }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 企业文化 -->
    <section class="company-culture">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:heart" />
            <span>企业文化</span>
          </div>
          <h2 class="section-title">核心价值观</h2>
          <p class="section-subtitle">指引我们前进的价值理念</p>
        </div>

        <div class="culture-grid">
          <div
            v-for="(value, index) in cultureValues"
            :key="index"
            class="culture-card"
          >
            <div class="culture-visual">
              <div class="culture-number">
                {{ String(index + 1).padStart(2, "0") }}
              </div>
              <div class="culture-icon">
                <Icon :icon="value.icon" />
              </div>
              <div class="visual-bg"></div>
            </div>

            <div class="culture-content">
              <h3 class="culture-title">{{ value.title }}</h3>
              <p class="culture-description">{{ value.description }}</p>

              <div class="culture-details">
                <div
                  class="detail"
                  v-for="detail in value.details"
                  :key="detail"
                >
                  <Icon icon="mdi:arrow-right-circle" class="detail-icon" />
                  <span>{{ detail }}</span>
                </div>
              </div>
            </div>

            <div class="culture-decorator"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- 愿景使命 -->
    <section class="vision-mission">
      <div class="vision-background">
        <div class="vision-overlay"></div>
        <div class="vision-pattern">
          <div v-for="i in 30" :key="i" class="pattern-dot"></div>
        </div>
      </div>

      <div class="container">
        <div class="vision-content">
          <div class="vision-item">
            <div class="vision-icon">
              <Icon icon="mdi:eye" />
            </div>
            <h2 class="vision-title">我们的愿景</h2>
            <p class="vision-text">
              成为智慧水利领域的领军企业，推动水利行业数字化转型，促进人与自然和谐发展。
            </p>
          </div>

          <div class="mission-item">
            <div class="mission-icon">
              <Icon icon="mdi:flag" />
            </div>
            <h2 class="mission-title">我们的使命</h2>
            <p class="mission-text">
              怀数智之力，护青绿山川，以科技创新驱动水利行业发展，构建水资源智能管理体系。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:handshake" />
              <span>携手合作</span>
            </div>
            <h2 class="cta-title">期待与您携手共创未来</h2>
            <p class="cta-subtitle">
              如果您对我们的服务感兴趣，或希望了解更多合作机会<br />
              <strong>欢迎随时与我们联系</strong>
            </p>

            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:map-marker" />
                <div>
                  <span class="contact-label">公司总部</span>
                  <span class="contact-value">北京市海淀区中关村科技园</span>
                </div>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:cog" />
                <div>
                  <span class="contact-label">研发中心</span>
                  <span class="contact-value">江苏省无锡市滨湖区雪浪小镇</span>
                </div>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <div>
                  <span class="contact-label">电子邮箱</span>
                  <span class="contact-value"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>

          <div class="cta-actions">
            <button class="btn-primary large" @click="handleContact">
              <Icon icon="mdi:phone" class="btn-icon" />
              <span>联系我们</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary large" @click="handleJoinUs">
              <Icon icon="mdi:account-plus" class="btn-icon" />
              <span>加入我们</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<style lang="scss" scoped>
@import url("@/assets/styles/about.scss");
</style>
