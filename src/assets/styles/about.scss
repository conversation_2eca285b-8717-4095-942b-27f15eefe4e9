@use "./variables.scss" as *;

.about {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 {
        animation-delay: 0.1s;
      }
      &.delay-2 {
        animation-delay: 0.2s;
      }
      &.delay-3 {
        animation-delay: 0.3s;
      }
      &.delay-4 {
        animation-delay: 0.4s;
      }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);
    z-index: -1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
    z-index: -1;
  }

  .hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .particle {
      position: absolute;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.3;

      &.particle-1 {
        width: 4px;
        height: 4px;
        top: 15%;
        left: 20%;
        @include floating-animation(3.2s, 12px, 0s);
      }
      &.particle-2 {
        width: 6px;
        height: 6px;
        top: 25%;
        left: 80%;
        @include floating-animation(4.1s, 18px, 0.5s);
      }
      &.particle-3 {
        width: 3px;
        height: 3px;
        top: 45%;
        left: 10%;
        @include floating-animation(3.8s, 15px, 1s);
      }
      &.particle-4 {
        width: 7px;
        height: 7px;
        top: 65%;
        left: 70%;
        @include floating-animation(4.5s, 22px, 1.5s);
      }
      &.particle-5 {
        width: 5px;
        height: 5px;
        top: 80%;
        left: 30%;
        @include floating-animation(3.6s, 14px, 2s);
      }
      &.particle-6 {
        width: 4px;
        height: 4px;
        top: 35%;
        left: 85%;
        @include floating-animation(4.2s, 16px, 2.5s);
      }
      &.particle-7 {
        width: 6px;
        height: 6px;
        top: 55%;
        left: 15%;
        @include floating-animation(3.9s, 20px, 0.8s);
      }
      &.particle-8 {
        width: 3px;
        height: 3px;
        top: 75%;
        left: 60%;
        @include floating-animation(4.3s, 11px, 1.8s);
      }
      &.particle-9 {
        width: 5px;
        height: 5px;
        top: 20%;
        left: 45%;
        @include floating-animation(3.7s, 17px, 2.2s);
      }
      &.particle-10 {
        width: 7px;
        height: 7px;
        top: 40%;
        left: 75%;
        @include floating-animation(4s, 19px, 1.2s);
      }
      &.particle-11 {
        width: 4px;
        height: 4px;
        top: 60%;
        left: 25%;
        @include floating-animation(4.4s, 13px, 0.3s);
      }
      &.particle-12 {
        width: 6px;
        height: 6px;
        top: 85%;
        left: 55%;
        @include floating-animation(3.5s, 21px, 2.8s);
      }
    }
  }

  .hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(5, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--primary-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--primary-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);
  }

  .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-alpha-20);
    border: 1px solid var(--primary-alpha-40);
    border-radius: var(--radius-2xl);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-8);
    @include glass-morphism(0.15, 16px, 0.3);

    .badge-icon {
      color: var(--electric-blue);
      font-size: var(--text-lg);
    }
  }

  .hero-title {
    font-size: var(--text-6xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-8);

    .title-line {
      display: block;
      margin-bottom: var(--space-2);
    }

    .title-highlight {
      @include gradient-text(var(--gradient-neon));
    }

    @media (max-width: 768px) {
      font-size: var(--text-4xl);
    }
  }

  .hero-subtitle {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    color: var(--sky-blue);
    margin-bottom: var(--space-10);

    strong {
      color: var(--white);
      @include gradient-text(var(--gradient-innovation));
    }
  }

  .hero-stats {
    display: flex;
    align-items: center;
    gap: var(--space-8);

    @media (max-width: 640px) {
      flex-direction: column;
      gap: var(--space-6);
    }

    .stat-item {
      text-align: center;

      .stat-icon {
        font-size: var(--text-2xl);
        color: var(--electric-blue);
        margin-bottom: var(--space-2);
      }

      .stat-number {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        color: var(--electric-blue);
        margin-bottom: var(--space-1);
        text-shadow: 0 0 10px var(--electric-blue);
      }

      .stat-label {
        font-size: var(--text-sm);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
      }
    }
  }

  // 视觉区域
  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;

    @media (max-width: 968px) {
      height: 400px;
      order: -1;
    }
  }

  .visual-container {
    position: relative;
    width: 500px;
    height: 500px;

    @media (max-width: 968px) {
      width: 350px;
      height: 350px;
    }
  }

  .company-hub {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .hub-core {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;

      .core-icon {
        font-size: 60px;
        color: var(--electric-blue);
        z-index: 3;
        position: relative;
        @include floating-animation(3s, 8px);
        filter: drop-shadow(0 0 30px var(--electric-blue));
      }

      .core-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
        background: radial-gradient(
          circle,
          var(--electric-blue) 0%,
          transparent 70%
        );
        border-radius: 50%;
        opacity: 0.3;
        animation: pulse 2s infinite;
      }
    }

    .hub-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid;
      border-radius: 50%;
      opacity: 0.6;

      &.ring-inner {
        width: 160px;
        height: 160px;
        border-color: var(--tech-alpha-50);
        animation: rotate 15s linear infinite;
      }

      &.ring-middle {
        width: 240px;
        height: 240px;
        border-color: var(--primary-alpha-30);
        animation: rotate 25s linear infinite reverse;
      }

      &.ring-outer {
        width: 320px;
        height: 320px;
        border-color: var(--electric-blue);
        opacity: 0.3;
        animation: rotate 35s linear infinite;
      }
    }
  }

  .value-constellation {
    .constellation-node {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);

      .node-icon {
        width: 50px;
        height: 50px;
        background: var(--gradient-glass);
        @include glass-morphism(0.2, 12px, 0.3);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--electric-blue);
        font-size: var(--text-xl);
        transition: var(--transition-all);

        &:hover {
          transform: scale(1.1);
          box-shadow: var(--glow-neon);
        }
      }

      .node-label {
        font-size: var(--text-xs);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
        text-align: center;
        white-space: nowrap;
      }

      &.node-1 {
        top: 15%;
        left: 25%;
        @include floating-animation(4s, 12px, 0s);
      }

      &.node-2 {
        top: 20%;
        right: 20%;
        @include floating-animation(3.5s, 10px, 0.5s);
      }

      &.node-3 {
        right: 10%;
        top: 50%;
        @include floating-animation(4.2s, 15px, 1s);
      }

      &.node-4 {
        bottom: 20%;
        left: 20%;
        @include floating-animation(3.8s, 11px, 1.5s);
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 公司介绍区域
.company-intro {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--white);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .intro-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
    }

    .intro-text {
      .intro-paragraph {
        display: flex;
        gap: var(--space-4);
        margin-bottom: var(--space-8);
        padding: var(--space-6);
        background: var(--gradient-subtle);
        border-radius: var(--radius-2xl);
        border: 1px solid var(--gray-200);
        transition: var(--transition-all);

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }

        .paragraph-icon {
          width: 50px;
          height: 50px;
          background: var(--gradient-primary);
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
          font-size: var(--text-xl);
          flex-shrink: 0;
          box-shadow: var(--glow-primary);
        }

        .paragraph-content {
          flex: 1;

          h3 {
            font-size: var(--text-xl);
            font-weight: var(--font-bold);
            color: var(--gray-900);
            margin-bottom: var(--space-3);
          }

          p {
            color: var(--gray-600);
            line-height: var(--leading-relaxed);
            font-size: var(--text-base);
          }
        }
      }
    }

    .intro-visual {
      display: flex;
      justify-content: center;
      align-items: center;

      .visual-container {
        position: relative;
        width: 400px;
        height: 400px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-3xl);
        overflow: hidden;
        box-shadow: var(--shadow-xl);

        @media (max-width: 768px) {
          width: 300px;
          height: 300px;
        }

        .tech-constellation {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .tech-node {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3);
            background: var(--gradient-glass);
            @include glass-morphism(0.2, 12px, 0.3);
            border-radius: var(--radius-xl);
            color: var(--white);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            transition: var(--transition-all);

            svg {
              font-size: var(--text-2xl);
              margin-bottom: var(--space-1);
            }

            &:hover {
              transform: scale(1.1);
              box-shadow: var(--glow-neon);
            }

            &.node-ai {
              top: 15%;
              left: 20%;
              @include floating-animation(4s, 12px, 0s);
            }

            &.node-data {
              top: 20%;
              right: 15%;
              @include floating-animation(3.5s, 10px, 0.5s);
            }

            &.node-cloud {
              right: 10%;
              top: 50%;
              @include floating-animation(4.2s, 15px, 1s);
            }

            &.node-iot {
              bottom: 20%;
              right: 20%;
              @include floating-animation(3.8s, 11px, 1.5s);
            }

            &.node-satellite {
              bottom: 15%;
              left: 15%;
              @include floating-animation(4.1s, 13px, 2s);
            }

            &.node-drone {
              left: 10%;
              top: 45%;
              @include floating-animation(3.7s, 9px, 2.5s);
            }
          }
        }
      }
    }
  }
}

// 核心团队区域
.core-team {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .team-grid {
    @include responsive-grid(auto-fit, 350px, var(--space-8));
  }

  .team-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -6px);

    .team-visual {
      position: relative;
      margin-bottom: var(--space-6);
      text-align: center;

      .team-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-3xl);
        margin: 0 auto;
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .team-bg {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 100px;
        height: 100px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: -1;
      }
    }

    .team-content {
      .team-name {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        text-align: center;
      }

      .team-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-6);
        font-size: var(--text-base);
        text-align: center;
      }

      .team-skills {
        .skills-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
          text-align: center;
        }

        .skills-list {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-2);
          justify-content: center;

          .skill-tag {
            background: var(--crystal-blue);
            color: var(--primary-blue);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-2xl);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
          }
        }
      }
    }

    &:hover {
      .team-visual .team-icon {
        transform: scale(1.1);
      }
    }
  }
}

// 发展历程区域
.development-timeline {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--white);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;

    .timeline-track {
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 2px;
      background: var(--gradient-primary);
      transform: translateX(-50%);
      z-index: 1;

      @media (max-width: 768px) {
        left: 30px;
      }
    }

    .milestone-item {
      position: relative;
      display: flex;
      align-items: flex-start;
      margin-bottom: var(--space-16);
      z-index: 2;

      @media (max-width: 768px) {
        margin-left: var(--space-16);
      }

      &:nth-child(even) {
        flex-direction: row-reverse;

        @media (max-width: 768px) {
          flex-direction: row;
        }

        .milestone-content {
          text-align: right;
          margin-right: var(--space-8);
          margin-left: 0;

          @media (max-width: 768px) {
            text-align: left;
            margin-right: 0;
            margin-left: var(--space-4);
          }
        }
      }

      .milestone-marker {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 3;

        .marker-inner {
          width: 80px;
          height: 80px;
          background: var(--gradient-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
          font-size: var(--text-2xl);
          box-shadow: var(--glow-primary);
          border: 4px solid var(--white);
        }

        .marker-year {
          position: absolute;
          bottom: -30px;
          background: var(--white);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-lg);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);
          box-shadow: var(--shadow-sm);
        }
      }

      .milestone-content {
        flex: 1;
        max-width: 300px;
        margin-left: var(--space-8);

        @media (max-width: 768px) {
          margin-left: var(--space-4);
          max-width: none;
        }

        .milestone-title {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--gray-900);
          margin-bottom: var(--space-3);
        }

        .milestone-description {
          color: var(--gray-600);
          line-height: var(--leading-relaxed);
          margin-bottom: var(--space-4);
          font-size: var(--text-base);
        }

        .milestone-achievements {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);

          .achievement {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);

            .achievement-icon {
              color: var(--success-green);
              font-size: var(--text-sm);
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}

// 企业文化区域
.company-culture {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .culture-grid {
    @include responsive-grid(auto-fit, 300px, var(--space-8));
  }

  .culture-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .culture-visual {
      position: relative;
      margin-bottom: var(--space-6);
      display: flex;
      align-items: center;
      gap: var(--space-4);

      .culture-number {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--primary-alpha-30);
        line-height: 1;
      }

      .culture-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-2xl);
        box-shadow: var(--glow-primary);
        position: relative;
        z-index: 2;
      }

      .visual-bg {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 80px;
        height: 80px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: 1;
      }
    }

    .culture-content {
      .culture-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
      }

      .culture-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
        font-size: var(--text-base);
      }

      .culture-details {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);

        .detail {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: var(--gray-700);

          .detail-icon {
            color: var(--success-green);
            font-size: var(--text-sm);
            flex-shrink: 0;
          }
        }
      }
    }

    .culture-decorator {
      position: absolute;
      bottom: -20px;
      right: -20px;
      width: 60px;
      height: 60px;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.1;
      transition: var(--transition-all);
    }

    &:hover {
      .culture-decorator {
        opacity: 0.2;
        transform: scale(1.2);
      }
    }
  }
}

// 愿景使命区域
.vision-mission {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .vision-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .vision-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .vision-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;

    .pattern-dot {
      position: absolute;
      width: 2px;
      height: 2px;
      background: var(--white);
      border-radius: 50%;

      &:nth-child(1) {
        top: 10%;
        left: 15%;
        animation: twinkle 3s infinite 0s;
      }
      &:nth-child(2) {
        top: 25%;
        left: 80%;
        animation: twinkle 4s infinite 0.5s;
      }
      &:nth-child(3) {
        top: 45%;
        left: 20%;
        animation: twinkle 3.5s infinite 1s;
      }
      &:nth-child(4) {
        top: 65%;
        left: 70%;
        animation: twinkle 4.5s infinite 1.5s;
      }
      &:nth-child(5) {
        top: 80%;
        left: 30%;
        animation: twinkle 3.2s infinite 2s;
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .vision-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }

    .vision-item,
    .mission-item {
      text-align: center;
      padding: var(--space-8);
      background: var(--gradient-glass);
      @include glass-morphism(0.1, 20px, 0.2);
      border-radius: var(--radius-2xl);
      border: 1px solid var(--white-alpha-20);

      .vision-icon,
      .mission-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-neon);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-6);
        font-size: var(--text-3xl);
        color: var(--white);
        box-shadow: var(--glow-neon);
      }

      .vision-title,
      .mission-title {
        font-size: var(--text-2xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-4);
        @include gradient-text(var(--gradient-neon));
      }

      .vision-text,
      .mission-text {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
      }
    }
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.1;
  }
  50% {
    opacity: 1;
  }
}

// CTA 区域
.cta-section {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .cta-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(4, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--white-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--white-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }

    .cta-text {
      .cta-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--white-alpha-20);
        border: 1px solid var(--white-alpha-30);
        border-radius: var(--radius-2xl);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        margin-bottom: var(--space-6);
        @include glass-morphism(0.1, 16px, 0.2);

        svg {
          font-size: var(--text-lg);
          color: var(--electric-blue);
        }
      }

      .cta-title {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-6);
        line-height: var(--leading-tight);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }

      .cta-subtitle {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
        margin-bottom: var(--space-8);

        strong {
          color: var(--electric-blue);
          font-weight: var(--font-bold);
        }
      }

      .contact-info {
        display: flex;
        flex-direction: column;
        gap: var(--space-4);

        .contact-item {
          display: flex;
          align-items: center;
          gap: var(--space-4);
          padding: var(--space-4);
          background: var(--gradient-glass);
          @include glass-morphism(0.1, 12px, 0.2);
          border-radius: var(--radius-xl);
          border: 1px solid var(--white-alpha-20);

          svg {
            font-size: var(--text-2xl);
            color: var(--electric-blue);
            flex-shrink: 0;
          }

          div {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);

            .contact-label {
              font-size: var(--text-sm);
              font-weight: var(--font-bold);
              color: var(--electric-blue);
            }

            .contact-value {
              font-size: var(--text-base);
              opacity: 0.9;
            }
          }
        }
      }
    }

    .cta-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
      align-items: flex-end;

      @media (max-width: 968px) {
        align-items: center;
      }

      button {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-2xl);
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);
        border: none;
        white-space: nowrap;

        &.large {
          padding: var(--space-5) var(--space-8);
          font-size: var(--text-lg);
        }

        .btn-icon {
          font-size: var(--text-xl);
        }

        .btn-arrow {
          font-size: var(--text-lg);
          transition: var(--transition-all);
        }

        &.btn-primary {
          background: var(--white);
          color: var(--primary-blue);

          &:hover {
            background: var(--crystal-blue);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);

            .btn-arrow {
              transform: translateX(4px);
            }
          }
        }

        &.btn-secondary {
          background: transparent;
          color: var(--white);
          border: 2px solid var(--white);

          &:hover {
            background: var(--white);
            color: var(--primary-blue);
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .hero-section {
    min-height: 80vh;

    .hero-title {
      font-size: var(--text-4xl);
    }

    .hero-subtitle {
      font-size: var(--text-base);
    }

    .hero-stats {
      flex-direction: column;
      gap: var(--space-4);
    }
  }

  .company-intro,
  .core-team,
  .company-culture {
    .intro-content,
    .team-grid,
    .culture-grid {
      grid-template-columns: 1fr;
    }
  }

  .development-timeline {
    .timeline-container {
      .milestone-item {
        &:nth-child(even) {
          flex-direction: row;

          .milestone-content {
            text-align: left;
            margin-right: 0;
            margin-left: var(--space-4);
          }
        }
      }
    }
  }
}

@media (max-width: 640px) {
  .hero-section {
    .hero-title {
      font-size: var(--text-3xl);
    }

    .hero-visual .visual-container {
      width: 250px;
      height: 250px;
    }
  }

  .company-intro,
  .core-team,
  .development-timeline,
  .company-culture,
  .vision-mission,
  .cta-section {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
  }

  .development-timeline {
    .timeline-container {
      .milestone-item {
        .milestone-marker .marker-inner {
          width: 60px;
          height: 60px;
          font-size: var(--text-xl);
        }
      }
    }
  }
}
